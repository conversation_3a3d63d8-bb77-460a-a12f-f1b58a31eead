import { useEffect } from 'react';
import { useRouter } from 'expo-router';
import { useAuth } from '@/contexts/AuthContext';
import { View, ActivityIndicator } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import IsotopeLogo from '@/components/IsotopeLogo';

export default function IndexScreen() {
  const { user, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    console.log('Index screen - loading:', loading, 'user:', user?.id);
    if (!loading) {
      if (user) {
        // User is authenticated, redirect to main app
        console.log('Redirecting to tabs');
        router.replace('/(tabs)');
      } else {
        // User is not authenticated, redirect to sign-in
        console.log('Redirecting to sign-in');
        router.replace('/(auth)/sign-in');
      }
    }
  }, [user, loading, router]);

  // Show loading screen while determining auth state
  return (
    <View style={{ flex: 1 }}>
      <LinearGradient
        colors={['#F8FAFC', '#F1F5F9']}
        style={{ 
          flex: 1, 
          justifyContent: 'center', 
          alignItems: 'center',
          paddingHorizontal: 20
        }}
      >
        <IsotopeLogo size="large" />
        <View style={{ marginTop: 40 }}>
          <ActivityIndicator size="large" color="#6366F1" />
        </View>
      </LinearGradient>
    </View>
  );
}
