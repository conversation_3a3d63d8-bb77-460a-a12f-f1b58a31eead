import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  TouchableOpacity,
} from 'react-native';

import { Shield, X, AlertTriangle, CheckCircle, Info } from 'lucide-react-native';

export interface NotificationData {
  id: string;
  type: 'success' | 'warning' | 'error' | 'info';
  title: string;
  message: string;
  duration?: number;
  action?: {
    label: string;
    onPress: () => void;
  };
}

interface BlockingNotificationsProps {
  notifications: NotificationData[];
  onDismiss: (id: string) => void;
}

const BlockingNotifications: React.FC<BlockingNotificationsProps> = ({
  notifications,
  onDismiss,
}) => {
  const [visibleNotifications, setVisibleNotifications] = useState<{
    [key: string]: Animated.Value;
  }>({});

  useEffect(() => {
    notifications.forEach(notification => {
      if (!visibleNotifications[notification.id]) {
        const animatedValue = new Animated.Value(0);
        setVisibleNotifications(prev => ({
          ...prev,
          [notification.id]: animatedValue,
        }));

        // Animate in
        Animated.spring(animatedValue, {
          toValue: 1,
          useNativeDriver: true,
          tension: 100,
          friction: 8,
        }).start();

        // Auto dismiss if duration is set
        if (notification.duration) {
          setTimeout(() => {
            handleDismiss(notification.id);
          }, notification.duration);
        }
      }
    });
  }, [notifications]);

  const handleDismiss = (id: string) => {
    const animatedValue = visibleNotifications[id];
    if (animatedValue) {
      Animated.timing(animatedValue, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start(() => {
        setVisibleNotifications(prev => {
          const newState = { ...prev };
          delete newState[id];
          return newState;
        });
        onDismiss(id);
      });
    } else {
      onDismiss(id);
    }
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'success':
        return <CheckCircle size={24} color="#10B981" />;
      case 'warning':
        return <AlertTriangle size={24} color="#F59E0B" />;
      case 'error':
        return <Shield size={24} color="#EF4444" />;
      default:
        return <Info size={24} color="#3B82F6" />;
    }
  };

  const getNotificationColors = (type: string) => {
    switch (type) {
      case 'success':
        return {
          background: '#FFFFFF',
          border: '#10B981',
          text: '#1F2937',
          accent: '#10B981',
        };
      case 'warning':
        return {
          background: '#FFFFFF',
          border: '#F59E0B',
          text: '#1F2937',
          accent: '#F59E0B',
        };
      case 'error':
        return {
          background: '#FFFFFF',
          border: '#EF4444',
          text: '#1F2937',
          accent: '#EF4444',
        };
      default:
        return {
          background: '#FFFFFF',
          border: '#6366F1',
          text: '#1F2937',
          accent: '#6366F1',
        };
    }
  };

  if (notifications.length === 0) {
    return null;
  }

  return (
    <View style={styles.container}>
      {notifications.map((notification, index) => {
        const animatedValue = visibleNotifications[notification.id];
        const colors = getNotificationColors(notification.type);

        if (!animatedValue) return null;

        return (
          <Animated.View
            key={notification.id}
            style={[
              styles.notification,
              {
                transform: [
                  {
                    translateY: animatedValue.interpolate({
                      inputRange: [0, 1],
                      outputRange: [-100, 0],
                    }),
                  },
                  {
                    scale: animatedValue.interpolate({
                      inputRange: [0, 1],
                      outputRange: [0.8, 1],
                    }),
                  },
                ],
                opacity: animatedValue,
                marginTop: index * 10,
              },
            ]}
          >
            <View
              style={[
                styles.notificationContent,
                {
                  borderColor: colors.border,
                  backgroundColor: colors.background,
                },
              ]}
            >
              <View style={styles.notificationHeader}>
                <View style={styles.iconContainer}>
                  {getNotificationIcon(notification.type)}
                </View>
                <View style={styles.textContainer}>
                  <Text style={[styles.title, { color: colors.text }]}>
                    {notification.title}
                  </Text>
                  <Text style={[styles.message, { color: colors.text }]}>
                    {notification.message}
                  </Text>
                </View>
                <TouchableOpacity
                  style={styles.dismissButton}
                  onPress={() => handleDismiss(notification.id)}
                >
                  <X size={20} color="#6B7280" />
                </TouchableOpacity>
              </View>

              {notification.action && (
                <View style={styles.actionContainer}>
                  <TouchableOpacity
                    style={[
                      styles.actionButton,
                      { borderColor: colors.border },
                    ]}
                    onPress={notification.action.onPress}
                  >
                    <Text style={[styles.actionText, { color: colors.text }]}>
                      {notification.action.label}
                    </Text>
                  </TouchableOpacity>
                </View>
              )}
            </View>
          </Animated.View>
        );
      })}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 60,
    left: 16,
    right: 16,
    zIndex: 1000,
  },
  notification: {
    marginBottom: 8,
  },
  notificationContent: {
    borderRadius: 16,
    borderWidth: 1,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  notificationHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  iconContainer: {
    marginRight: 12,
    marginTop: 2,
  },
  textContainer: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 4,
  },
  message: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    lineHeight: 20,
    opacity: 0.8,
  },
  dismissButton: {
    padding: 4,
    marginLeft: 8,
  },
  actionContainer: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.1)',
  },
  actionButton: {
    borderWidth: 1,
    borderRadius: 8,
    paddingVertical: 8,
    paddingHorizontal: 16,
    alignSelf: 'flex-start',
  },
  actionText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
  },
});

export default BlockingNotifications;
