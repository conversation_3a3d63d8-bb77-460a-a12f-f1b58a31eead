import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Animated,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import {
  Smartphone,
  Shield,
  Play,
  Pause,
  RotateCcw,
  Zap,
  Target,
  Clock,
} from 'lucide-react-native';
import { useDistractionBlocking } from '@/hooks/useDistractionBlocking';

interface DistractionBlockingDemoProps {
  isVisible: boolean;
}

const DistractionBlockingDemo: React.FC<DistractionBlockingDemoProps> = ({
  isVisible,
}) => {
  const { simulateAppBlock, recentlyBlockedApps, stats } = useDistractionBlocking();
  const [isRunning, setIsRunning] = useState(false);
  const [demoStep, setDemoStep] = useState(0);
  const [animatedValue] = useState(new Animated.Value(0));

  const demoApps = [
    { name: 'Instagram', packageName: 'com.instagram.android', icon: '📷' },
    { name: 'TikTok', packageName: 'com.zhiliaoapp.musically', icon: '🎵' },
    { name: 'YouTube', packageName: 'com.google.android.youtube', icon: '📺' },
    { name: 'Twitter', packageName: 'com.twitter.android', icon: '🐦' },
    { name: 'Facebook', packageName: 'com.facebook.katana', icon: '📘' },
  ];

  const demoSteps = [
    'Starting focus session...',
    'Detecting app switch attempt...',
    'Blocking distraction...',
    'Showing notification...',
    'Recording attempt...',
    'Demo complete!',
  ];

  React.useEffect(() => {
    if (isVisible) {
      Animated.spring(animatedValue, {
        toValue: 1,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }).start();
    } else {
      Animated.timing(animatedValue, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [isVisible]);

  const runDemo = async () => {
    setIsRunning(true);
    setDemoStep(0);

    for (let i = 0; i < demoSteps.length; i++) {
      setDemoStep(i);
      await new Promise(resolve => setTimeout(resolve, 1000));

      if (i === 2) {
        // Simulate blocking a random app
        const randomApp = demoApps[Math.floor(Math.random() * demoApps.length)];
        simulateAppBlock(randomApp.name, randomApp.packageName);
      }
    }

    setIsRunning(false);
    setDemoStep(0);
  };

  const resetDemo = () => {
    setIsRunning(false);
    setDemoStep(0);
  };

  if (!isVisible) {
    return null;
  }

  return (
    <Animated.View
      style={[
        styles.container,
        {
          transform: [
            {
              scale: animatedValue.interpolate({
                inputRange: [0, 1],
                outputRange: [0.9, 1],
              }),
            },
          ],
          opacity: animatedValue,
        },
      ]}
    >
      <LinearGradient
        colors={['rgba(99, 102, 241, 0.1)', 'rgba(99, 102, 241, 0.05)']}
        style={styles.demoCard}
      >
        <View style={styles.header}>
          <View style={styles.iconContainer}>
            <Shield size={24} color="#6366F1" />
          </View>
          <View style={styles.headerText}>
            <Text style={styles.title}>Blocking Demo</Text>
            <Text style={styles.subtitle}>
              Test how distraction blocking works in real-time
            </Text>
          </View>
        </View>

        <View style={styles.content}>
          {/* Demo Controls */}
          <View style={styles.controls}>
            <TouchableOpacity
              style={[
                styles.controlButton,
                isRunning && styles.controlButtonDisabled,
              ]}
              onPress={runDemo}
              disabled={isRunning}
            >
              <LinearGradient
                colors={isRunning ? ['#9CA3AF', '#6B7280'] : ['#6366F1', '#4F46E5']}
                style={styles.controlButtonGradient}
              >
                {isRunning ? (
                  <Pause size={20} color="#FFFFFF" />
                ) : (
                  <Play size={20} color="#FFFFFF" />
                )}
                <Text style={styles.controlButtonText}>
                  {isRunning ? 'Running...' : 'Start Demo'}
                </Text>
              </LinearGradient>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.resetButton}
              onPress={resetDemo}
              disabled={isRunning}
            >
              <RotateCcw size={20} color="#6B7280" />
            </TouchableOpacity>
          </View>

          {/* Demo Progress */}
          {isRunning && (
            <View style={styles.progress}>
              <Text style={styles.progressTitle}>Demo Progress</Text>
              <View style={styles.progressSteps}>
                {demoSteps.map((step, index) => (
                  <View
                    key={index}
                    style={[
                      styles.progressStep,
                      index <= demoStep && styles.progressStepActive,
                      index === demoStep && styles.progressStepCurrent,
                    ]}
                  >
                    <View style={styles.progressStepIcon}>
                      {index <= demoStep ? (
                        <Target size={16} color="#10B981" />
                      ) : (
                        <Clock size={16} color="#9CA3AF" />
                      )}
                    </View>
                    <Text
                      style={[
                        styles.progressStepText,
                        index <= demoStep && styles.progressStepTextActive,
                      ]}
                    >
                      {step}
                    </Text>
                  </View>
                ))}
              </View>
            </View>
          )}

          {/* Demo Apps */}
          <View style={styles.demoApps}>
            <Text style={styles.sectionTitle}>Apps to Test</Text>
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              style={styles.appsScroll}
            >
              {demoApps.map((app, index) => (
                <TouchableOpacity
                  key={index}
                  style={styles.appItem}
                  onPress={() => simulateAppBlock(app.name, app.packageName)}
                  disabled={isRunning}
                >
                  <View style={styles.appIcon}>
                    <Text style={styles.appIconText}>{app.icon}</Text>
                  </View>
                  <Text style={styles.appName}>{app.name}</Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>

          {/* Recent Blocks */}
          {recentlyBlockedApps.length > 0 && (
            <View style={styles.recentBlocks}>
              <Text style={styles.sectionTitle}>Recent Blocks</Text>
              {recentlyBlockedApps.slice(0, 3).map((app, index) => (
                <View key={index} style={styles.blockItem}>
                  <View style={styles.blockIcon}>
                    <Shield size={16} color="#EF4444" />
                  </View>
                  <Text style={styles.blockText}>
                    {app.name} - {app.attempts} attempt{app.attempts !== 1 ? 's' : ''}
                  </Text>
                  <Text style={styles.blockTime}>
                    {new Date(app.lastAttempt).toLocaleTimeString()}
                  </Text>
                </View>
              ))}
            </View>
          )}

          {/* Stats */}
          <View style={styles.stats}>
            <View style={styles.statItem}>
              <Zap size={20} color="#F59E0B" />
              <Text style={styles.statLabel}>Blocked Today</Text>
              <Text style={styles.statValue}>{stats.blockedToday}</Text>
            </View>
            <View style={styles.statItem}>
              <Target size={20} color="#10B981" />
              <Text style={styles.statLabel}>Effectiveness</Text>
              <Text style={styles.statValue}>{stats.blockingEffectiveness}%</Text>
            </View>
          </View>
        </View>
      </LinearGradient>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: 16,
  },
  demoCard: {
    borderRadius: 16,
    borderWidth: 1,
    borderColor: 'rgba(99, 102, 241, 0.2)',
    padding: 20,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 12,
    backgroundColor: 'rgba(99, 102, 241, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  headerText: {
    flex: 1,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
    color: '#6B7280',
  },
  content: {
    gap: 16,
  },
  controls: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  controlButton: {
    flex: 1,
  },
  controlButtonDisabled: {
    opacity: 0.6,
  },
  controlButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 12,
    gap: 8,
  },
  controlButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  resetButton: {
    width: 48,
    height: 48,
    borderRadius: 12,
    backgroundColor: 'rgba(107, 114, 128, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  progress: {
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
    borderRadius: 12,
    padding: 16,
  },
  progressTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 12,
  },
  progressSteps: {
    gap: 8,
  },
  progressStep: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  progressStepActive: {
    opacity: 1,
  },
  progressStepCurrent: {
    backgroundColor: 'rgba(16, 185, 129, 0.1)',
    borderRadius: 8,
    padding: 8,
    marginHorizontal: -8,
  },
  progressStepIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: 'rgba(156, 163, 175, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  progressStepText: {
    fontSize: 14,
    color: '#9CA3AF',
  },
  progressStepTextActive: {
    color: '#1F2937',
    fontWeight: '500',
  },
  demoApps: {},
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 12,
  },
  appsScroll: {
    marginHorizontal: -4,
  },
  appItem: {
    alignItems: 'center',
    marginHorizontal: 8,
    padding: 12,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
    minWidth: 80,
  },
  appIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(99, 102, 241, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  appIconText: {
    fontSize: 20,
  },
  appName: {
    fontSize: 12,
    color: '#6B7280',
    textAlign: 'center',
  },
  recentBlocks: {},
  blockItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: 'rgba(239, 68, 68, 0.05)',
    borderRadius: 8,
    marginBottom: 4,
  },
  blockIcon: {
    marginRight: 8,
  },
  blockText: {
    flex: 1,
    fontSize: 14,
    color: '#1F2937',
  },
  blockTime: {
    fontSize: 12,
    color: '#6B7280',
  },
  stats: {
    flexDirection: 'row',
    gap: 16,
  },
  statItem: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
    borderRadius: 12,
    padding: 12,
    gap: 8,
  },
  statLabel: {
    flex: 1,
    fontSize: 14,
    color: '#6B7280',
  },
  statValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
  },
});

export default DistractionBlockingDemo;
