// !$*UTF8*$!
{
	archiveVersion = 1;
	objectVersion = 54;
	classes = {
	};
	objects = {

/* Begin PBXBuildFile section */
		13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		3E461D99554A48A4959DE609 /* SplashScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = AA286B85B6C04FC6940260E9 /* SplashScreen.storyboard */; };
		455E0B83717602158362555E /* libPods-IsotopeAI.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 37A2C18E67316E996C533670 /* libPods-IsotopeAI.a */; };
		7D97DE5C402C956999B7CDC8 /* ExpoModulesProvider.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9CAA9DA37380BD9A0781D520 /* ExpoModulesProvider.swift */; };
		B7E5F51733B0AEF6C4FE7494 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 8B09BDBD111CBD0321B55340 /* PrivacyInfo.xcprivacy */; };
		BB2F792D24A3F905000567C9 /* Expo.plist in Resources */ = {isa = PBXBuildFile; fileRef = BB2F792C24A3F905000567C9 /* Expo.plist */; };
		F11748422D0307B40044C1D9 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = F11748412D0307B40044C1D9 /* AppDelegate.swift */; };
		XX103037AC5A7B821341EBXX /* ShieldConfiguration.appex in Embed Foundation Extensions */ = {isa = PBXBuildFile; fileRef = XXEC8F4A23169846583657XX /* ShieldConfiguration.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		XX44FC18D28196FBCB6075XX /* ManagedSettingsUI.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = XXB6D82409997898620841XX /* ManagedSettingsUI.framework */; };
		XXBEFFB36AB412820C306DXX /* ShieldAction.appex in Embed Foundation Extensions */ = {isa = PBXBuildFile; fileRef = XX05E83E103BF49F57A457XX /* ShieldAction.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		XXD33E402502F114432AD3XX /* DeviceActivity.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = XXCA68A9416C78B970D1D9XX /* DeviceActivity.framework */; };
		XXE5C6ED1B941143A02B1EXX /* ActivityMonitorExtension.appex in Embed Foundation Extensions */ = {isa = PBXBuildFile; fileRef = XX7DDE620121E0E26B18E0XX /* ActivityMonitorExtension.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		XX7D740BCEA9150F638357XX /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 83CBB9F71A601CBA00E9B192 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = XX3F0FEC3A3780043C5071XX;
			remoteInfo = ShieldConfiguration;
		};
		XX9C2CABAF796184CCCE70XX /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 83CBB9F71A601CBA00E9B192 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = XXA10EF079A7E8D1784A82XX;
			remoteInfo = ActivityMonitorExtension;
		};
		XXDDE06CF18B5502AA5505XX /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 83CBB9F71A601CBA00E9B192 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = XX3D32D7C943D37D77BB1BXX;
			remoteInfo = ShieldAction;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		XX72A8CFBF0FBA13A79902XX /* Embed Foundation Extensions */ = {
			isa = PBXCopyFilesBuildPhase;
			name = "Embed Foundation Extensions";
			files = (
				XXE5C6ED1B941143A02B1EXX /* ActivityMonitorExtension.appex in Embed Foundation Extensions */,
				XXBEFFB36AB412820C306DXX /* ShieldAction.appex in Embed Foundation Extensions */,
				XX103037AC5A7B821341EBXX /* ShieldConfiguration.appex in Embed Foundation Extensions */,
			);
			dstPath = "";
			dstSubfolderSpec = 13;
			buildActionMask = **********;
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		0D786A50EF7ABD459845F80B /* Pods-IsotopeAI.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-IsotopeAI.release.xcconfig"; path = "Target Support Files/Pods-IsotopeAI/Pods-IsotopeAI.release.xcconfig"; sourceTree = "<group>"; fileEncoding = 4; };
		13B07F961A680F5B00A75B9A /* IsotopeAI.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = IsotopeAI.app; sourceTree = BUILT_PRODUCTS_DIR; fileEncoding = 4; };
		13B07FB51A68108700A75B9A /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; name = Images.xcassets; path = IsotopeAI/Images.xcassets; sourceTree = "<group>"; fileEncoding = 4; includeInIndex = 0; };
		13B07FB61A68108700A75B9A /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = Info.plist; path = IsotopeAI/Info.plist; sourceTree = "<group>"; includeInIndex = 0; };
		37A2C18E67316E996C533670 /* libPods-IsotopeAI.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-IsotopeAI.a"; sourceTree = BUILT_PRODUCTS_DIR; fileEncoding = 4; };
		67034C1C2E8FBF75BC10D542 /* Pods-IsotopeAI.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-IsotopeAI.debug.xcconfig"; path = "Target Support Files/Pods-IsotopeAI/Pods-IsotopeAI.debug.xcconfig"; sourceTree = "<group>"; fileEncoding = 4; };
		8B09BDBD111CBD0321B55340 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; name = PrivacyInfo.xcprivacy; path = IsotopeAI/PrivacyInfo.xcprivacy; sourceTree = "<group>"; fileEncoding = 4; lastKnownFileType = undefined; };
		9CAA9DA37380BD9A0781D520 /* ExpoModulesProvider.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ExpoModulesProvider.swift; path = "Pods/Target Support Files/Pods-IsotopeAI/ExpoModulesProvider.swift"; sourceTree = "<group>"; fileEncoding = 4; };
		AA286B85B6C04FC6940260E9 /* SplashScreen.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; name = SplashScreen.storyboard; path = IsotopeAI/SplashScreen.storyboard; sourceTree = "<group>"; includeInIndex = 0; };
		BB2F792C24A3F905000567C9 /* Expo.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = Expo.plist; sourceTree = "<group>"; includeInIndex = 0; };
		ED297162215061F000B7C4FE /* JavaScriptCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = JavaScriptCore.framework; path = System/Library/Frameworks/JavaScriptCore.framework; sourceTree = SDKROOT; fileEncoding = 4; includeInIndex = undefined; };
		F11748412D0307B40044C1D9 /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; name = AppDelegate.swift; path = IsotopeAI/AppDelegate.swift; sourceTree = "<group>"; fileEncoding = 4; includeInIndex = 0; };
		F11748442D0722820044C1D9 /* IsotopeAI-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = "IsotopeAI-Bridging-Header.h"; path = "IsotopeAI/IsotopeAI-Bridging-Header.h"; sourceTree = "<group>"; fileEncoding = 4; includeInIndex = 0; };
		XX05E83E103BF49F57A457XX /* ShieldAction.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = ShieldAction.appex; sourceTree = BUILT_PRODUCTS_DIR; fileEncoding = 4; };
		XX7DDE620121E0E26B18E0XX /* ActivityMonitorExtension.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = ActivityMonitorExtension.appex; sourceTree = BUILT_PRODUCTS_DIR; fileEncoding = 4; };
		XXB6D82409997898620841XX /* ManagedSettingsUI.framework */ = {isa = PBXFileReference; path = System/Library/Frameworks/ManagedSettingsUI.framework; fileEncoding = 4; lastKnownFileType = wrapper.framework; includeInIndex = undefined; name = ManagedSettingsUI.framework; sourceTree = SDKROOT; };
		XXCA68A9416C78B970D1D9XX /* DeviceActivity.framework */ = {isa = PBXFileReference; path = System/Library/Frameworks/DeviceActivity.framework; fileEncoding = 4; lastKnownFileType = wrapper.framework; includeInIndex = undefined; name = DeviceActivity.framework; sourceTree = SDKROOT; };
		XXEC8F4A23169846583657XX /* ShieldConfiguration.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = ShieldConfiguration.appex; sourceTree = BUILT_PRODUCTS_DIR; fileEncoding = 4; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		XX0B569855547E5232B872XX /* PBXFileSystemSynchronizedBuildFileExceptionSet */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			target = XXA10EF079A7E8D1784A82XX /* ActivityMonitorExtension */;
			membershipExceptions = (
				Info.plist,
				"expo-target.config.js",
			);
		};
		XX332839F488E4B22EBE1BXX /* PBXFileSystemSynchronizedBuildFileExceptionSet */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			target = XX3D32D7C943D37D77BB1BXX /* ShieldAction */;
			membershipExceptions = (
				Info.plist,
				"expo-target.config.js",
			);
		};
		XX8F16162D2E73FEB3F511XX /* PBXFileSystemSynchronizedBuildFileExceptionSet */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			target = XX3F0FEC3A3780043C5071XX /* ShieldConfiguration */;
			membershipExceptions = (
				Info.plist,
				"expo-target.config.js",
			);
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		XX5819063958910BDDB5ABXX /* ShieldAction */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = ShieldAction;
			exceptions = (
				XX332839F488E4B22EBE1BXX /* PBXFileSystemSynchronizedBuildFileExceptionSet */,
			);
			explicitFileTypes = {};
			explicitFolders = (
			);
			sourceTree = "<group>";
		};
		XX585A33AC666D11AD965CXX /* ActivityMonitorExtension */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = ActivityMonitorExtension;
			exceptions = (
				XX0B569855547E5232B872XX /* PBXFileSystemSynchronizedBuildFileExceptionSet */,
			);
			explicitFileTypes = {};
			explicitFolders = (
			);
			sourceTree = "<group>";
		};
		XXA714724ADC07B5B0E74EXX /* ShieldConfiguration */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = ShieldConfiguration;
			exceptions = (
				XX8F16162D2E73FEB3F511XX /* PBXFileSystemSynchronizedBuildFileExceptionSet */,
			);
			explicitFileTypes = {};
			explicitFolders = (
			);
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		13B07F8C1A680F5B00A75B9A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				455E0B83717602158362555E /* libPods-IsotopeAI.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		XX074B8811C1CF8E8A3D74XX /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			runOnlyForDeploymentPostprocessing = 0;
			files = (
				XXD33E402502F114432AD3XX /* DeviceActivity.framework in Frameworks */,
			);
		};
		XXBA6CDF0021ABEEEDFCFBXX /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			runOnlyForDeploymentPostprocessing = 0;
			files = (
				XX44FC18D28196FBCB6075XX /* ManagedSettingsUI.framework in Frameworks */,
			);
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		13B07FAE1A68108700A75B9A /* IsotopeAI */ = {
			isa = PBXGroup;
			children = (
				F11748412D0307B40044C1D9 /* AppDelegate.swift */,
				F11748442D0722820044C1D9 /* IsotopeAI-Bridging-Header.h */,
				BB2F792B24A3F905000567C9 /* Supporting */,
				13B07FB51A68108700A75B9A /* Images.xcassets */,
				13B07FB61A68108700A75B9A /* Info.plist */,
				AA286B85B6C04FC6940260E9 /* SplashScreen.storyboard */,
				8B09BDBD111CBD0321B55340 /* PrivacyInfo.xcprivacy */,
			);
			name = IsotopeAI;
			sourceTree = "<group>";
		};
		2D16E6871FA4F8E400B85C8A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				ED297162215061F000B7C4FE /* JavaScriptCore.framework */,
				37A2C18E67316E996C533670 /* libPods-IsotopeAI.a */,
				XXCA68A9416C78B970D1D9XX /* DeviceActivity.framework */,
				XXB6D82409997898620841XX /* ManagedSettingsUI.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		832341AE1AAA6A7D00B99B32 /* Libraries */ = {
			isa = PBXGroup;
			children = (
			);
			name = Libraries;
			sourceTree = "<group>";
		};
		83CBB9F61A601CBA00E9B192 = {
			isa = PBXGroup;
			children = (
				XX3E41C1850246162C0061XX /* expo:targets */,
				13B07FAE1A68108700A75B9A /* IsotopeAI */,
				832341AE1AAA6A7D00B99B32 /* Libraries */,
				83CBBA001A601CBA00E9B192 /* Products */,
				2D16E6871FA4F8E400B85C8A /* Frameworks */,
				DD8AC80DFA9F8461F11B9991 /* Pods */,
				DF4DDE1CFB2B73B6E02F17DE /* ExpoModulesProviders */,
			);
			indentWidth = 2;
			sourceTree = "<group>";
			tabWidth = 2;
			usesTabs = 0;
		};
		83CBBA001A601CBA00E9B192 /* Products */ = {
			isa = PBXGroup;
			children = (
				13B07F961A680F5B00A75B9A /* IsotopeAI.app */,
				XX7DDE620121E0E26B18E0XX /* ActivityMonitorExtension.appex */,
				XX05E83E103BF49F57A457XX /* ShieldAction.appex */,
				XXEC8F4A23169846583657XX /* ShieldConfiguration.appex */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		BB2F792B24A3F905000567C9 /* Supporting */ = {
			isa = PBXGroup;
			children = (
				BB2F792C24A3F905000567C9 /* Expo.plist */,
			);
			name = Supporting;
			path = IsotopeAI/Supporting;
			sourceTree = "<group>";
		};
		DD8AC80DFA9F8461F11B9991 /* Pods */ = {
			isa = PBXGroup;
			children = (
				67034C1C2E8FBF75BC10D542 /* Pods-IsotopeAI.debug.xcconfig */,
				0D786A50EF7ABD459845F80B /* Pods-IsotopeAI.release.xcconfig */,
			);
			name = Pods;
			path = Pods;
			sourceTree = "<group>";
		};
		DF4DDE1CFB2B73B6E02F17DE /* ExpoModulesProviders */ = {
			isa = PBXGroup;
			children = (
				FF7690913C6E8FD0CD001D82 /* IsotopeAI */,
			);
			name = ExpoModulesProviders;
			sourceTree = "<group>";
		};
		FF7690913C6E8FD0CD001D82 /* IsotopeAI */ = {
			isa = PBXGroup;
			children = (
				9CAA9DA37380BD9A0781D520 /* ExpoModulesProvider.swift */,
			);
			name = IsotopeAI;
			sourceTree = "<group>";
		};
		XX3E41C1850246162C0061XX /* expo:targets */ = {
			isa = PBXGroup;
			children = (
				XX585A33AC666D11AD965CXX /* ActivityMonitorExtension */,
				XX5819063958910BDDB5ABXX /* ShieldAction */,
				XXA714724ADC07B5B0E74EXX /* ShieldConfiguration */,
			);
			sourceTree = "<group>";
			name = expo:targets;
			path = ../targets;
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		13B07F861A680F5B00A75B9A /* IsotopeAI */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "IsotopeAI" */;
			buildPhases = (
				08A4A3CD28434E44B6B9DE2E /* [CP] Check Pods Manifest.lock */,
				27E8FD2958669CDA37EA6615 /* [Expo] Configure project */,
				13B07F871A680F5B00A75B9A /* Sources */,
				13B07F8C1A680F5B00A75B9A /* Frameworks */,
				13B07F8E1A680F5B00A75B9A /* Resources */,
				00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */,
				800E24972A6A228C8D4807E9 /* [CP] Copy Pods Resources */,
				344A1832361FE130FC20486C /* [CP] Embed Pods Frameworks */,
				XX72A8CFBF0FBA13A79902XX /* Embed Foundation Extensions */,
			);
			buildRules = (
			);
			dependencies = (
				XXFE818A28A5955E5D90A3XX /* PBXTargetDependency */,
				XXF510CD31F2FCFD77BC98XX /* PBXTargetDependency */,
				XX4B53910FF94CC8DD0B2AXX /* PBXTargetDependency */,
			);
			name = IsotopeAI;
			productName = IsotopeAI;
			productReference = 13B07F961A680F5B00A75B9A /* IsotopeAI.app */;
			productType = "com.apple.product-type.application";
		};
		XX3D32D7C943D37D77BB1BXX /* ShieldAction */ = {
			isa = PBXNativeTarget;
			buildPhases = (
				XXBA6CDF0021ABEEEDFCFBXX /* Frameworks */,
				XXDC105B0E28F884B8E02DXX /* Sources */,
				XXC55EF014B50CBDECCD15XX /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			buildConfigurationList = XXA7EB4A2DFBC8FC5DB310XX /* Build configuration list for PBXNativeTarget "ShieldAction" */;
			name = ShieldAction;
			productName = ShieldAction;
			productReference = XX05E83E103BF49F57A457XX /* ShieldAction.appex */;
			productType = "com.apple.product-type.app-extension";
			fileSystemSynchronizedGroups = (
				XX5819063958910BDDB5ABXX /* ShieldAction */,
			);
		};
		XX3F0FEC3A3780043C5071XX /* ShieldConfiguration */ = {
			isa = PBXNativeTarget;
			buildPhases = (
				XXE5EC32CD2D09C6C886C7XX /* Sources */,
				XX00779A11A1B70B580100XX /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			buildConfigurationList = XXBB75734F95ED04335AF2XX /* Build configuration list for PBXNativeTarget "ShieldConfiguration" */;
			name = ShieldConfiguration;
			productName = ShieldConfiguration;
			productReference = XXEC8F4A23169846583657XX /* ShieldConfiguration.appex */;
			productType = "com.apple.product-type.app-extension";
			fileSystemSynchronizedGroups = (
				XXA714724ADC07B5B0E74EXX /* ShieldConfiguration */,
			);
		};
		XXA10EF079A7E8D1784A82XX /* ActivityMonitorExtension */ = {
			isa = PBXNativeTarget;
			buildPhases = (
				XX074B8811C1CF8E8A3D74XX /* Frameworks */,
				XXE45443DC38DCFE1DB622XX /* Sources */,
				XXFA5A8CCDD805DF7AEF9CXX /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			buildConfigurationList = XX3733CB6C1678C0F8E742XX /* Build configuration list for PBXNativeTarget "ActivityMonitorExtension" */;
			name = ActivityMonitorExtension;
			productName = ActivityMonitorExtension;
			productReference = XX7DDE620121E0E26B18E0XX /* ActivityMonitorExtension.appex */;
			productType = "com.apple.product-type.app-extension";
			fileSystemSynchronizedGroups = (
				XX585A33AC666D11AD965CXX /* ActivityMonitorExtension */,
			);
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		83CBB9F71A601CBA00E9B192 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 1130;
				TargetAttributes = {
					13B07F861A680F5B00A75B9A = {
						LastSwiftMigration = 1250;
					};
					XXA10EF079A7E8D1784A82XX = {
						CreatedOnToolsVersion = 14.3;
						ProvisioningStyle = Automatic;
						DevelopmentTeam = undefined;
					};
					XX3D32D7C943D37D77BB1BXX = {
						CreatedOnToolsVersion = 14.3;
						ProvisioningStyle = Automatic;
						DevelopmentTeam = undefined;
					};
					XX3F0FEC3A3780043C5071XX = {
						CreatedOnToolsVersion = 14.3;
						ProvisioningStyle = Automatic;
						DevelopmentTeam = undefined;
					};
				};
			};
			buildConfigurationList = 83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "IsotopeAI" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 83CBB9F61A601CBA00E9B192;
			productRefGroup = 83CBBA001A601CBA00E9B192 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				13B07F861A680F5B00A75B9A /* IsotopeAI */,
				XXA10EF079A7E8D1784A82XX /* ActivityMonitorExtension */,
				XX3D32D7C943D37D77BB1BXX /* ShieldAction */,
				XX3F0FEC3A3780043C5071XX /* ShieldConfiguration */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		13B07F8E1A680F5B00A75B9A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				BB2F792D24A3F905000567C9 /* Expo.plist in Resources */,
				13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */,
				3E461D99554A48A4959DE609 /* SplashScreen.storyboard in Resources */,
				B7E5F51733B0AEF6C4FE7494 /* PrivacyInfo.xcprivacy in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		XX00779A11A1B70B580100XX /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			runOnlyForDeploymentPostprocessing = 0;
			files = (
			);
		};
		XXC55EF014B50CBDECCD15XX /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			runOnlyForDeploymentPostprocessing = 0;
			files = (
			);
		};
		XXFA5A8CCDD805DF7AEF9CXX /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			runOnlyForDeploymentPostprocessing = 0;
			files = (
			);
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "if [[ -f \"$PODS_ROOT/../.xcode.env\" ]]; then\n  source \"$PODS_ROOT/../.xcode.env\"\nfi\nif [[ -f \"$PODS_ROOT/../.xcode.env.local\" ]]; then\n  source \"$PODS_ROOT/../.xcode.env.local\"\nfi\n\n# The project root by default is one level up from the ios directory\nexport PROJECT_ROOT=\"$PROJECT_DIR\"/..\n\nif [[ \"$CONFIGURATION\" = *Debug* ]]; then\n  export SKIP_BUNDLING=1\nfi\nif [[ -z \"$ENTRY_FILE\" ]]; then\n  # Set the entry JS file using the bundler's entry resolution.\n  export ENTRY_FILE=\"$(\"$NODE_BINARY\" -e \"require('expo/scripts/resolveAppEntry')\" \"$PROJECT_ROOT\" ios absolute | tail -n 1)\"\nfi\n\nif [[ -z \"$CLI_PATH\" ]]; then\n  # Use Expo CLI\n  export CLI_PATH=\"$(\"$NODE_BINARY\" --print \"require.resolve('@expo/cli', { paths: [require.resolve('expo/package.json')] })\")\"\nfi\nif [[ -z \"$BUNDLE_COMMAND\" ]]; then\n  # Default Expo CLI command for bundling\n  export BUNDLE_COMMAND=\"export:embed\"\nfi\n\n# Source .xcode.env.updates if it exists to allow\n# SKIP_BUNDLING to be unset if needed\nif [[ -f \"$PODS_ROOT/../.xcode.env.updates\" ]]; then\n  source \"$PODS_ROOT/../.xcode.env.updates\"\nfi\n# Source local changes to allow overrides\n# if needed\nif [[ -f \"$PODS_ROOT/../.xcode.env.local\" ]]; then\n  source \"$PODS_ROOT/../.xcode.env.local\"\nfi\n\n`\"$NODE_BINARY\" --print \"require('path').dirname(require.resolve('react-native/package.json')) + '/scripts/react-native-xcode.sh'\"`\n\n";
			outputFileListPaths = (
			);
			inputFileListPaths = (
			);
		};
		08A4A3CD28434E44B6B9DE2E /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-IsotopeAI-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		27E8FD2958669CDA37EA6615 /* [Expo] Configure project */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "[Expo] Configure project";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "# This script configures Expo modules and generates the modules provider file.\nbash -l -c \"./Pods/Target\\ Support\\ Files/Pods-IsotopeAI/expo-configure-project.sh\"\n";
		};
		344A1832361FE130FC20486C /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-IsotopeAI/Pods-IsotopeAI-frameworks.sh",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/hermes-engine/Pre-built/hermes.framework/hermes",
			);
			name = "[CP] Embed Pods Frameworks";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/hermes.framework",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-IsotopeAI/Pods-IsotopeAI-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
			outputFileListPaths = (
			);
			inputFileListPaths = (
			);
		};
		800E24972A6A228C8D4807E9 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-IsotopeAI/Pods-IsotopeAI-resources.sh",
				"${PODS_CONFIGURATION_BUILD_DIR}/EXApplication/ExpoApplication_privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/EXConstants/EXConstants.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/EXConstants/ExpoConstants_privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/ExpoDevice/ExpoDevice_privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/ExpoFileSystem/ExpoFileSystem_privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/ExpoSystemUI/ExpoSystemUI_privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/RCT-Folly/RCT-Folly_privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/RNCAsyncStorage/RNCAsyncStorage_resources.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/RNSVG/RNSVGFilters.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/React-Core/React-Core_privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/React-cxxreact/React-cxxreact_privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/boost/boost_privacy.bundle",
				"${PODS_CONFIGURATION_BUILD_DIR}/glog/glog_privacy.bundle",
			);
			name = "[CP] Copy Pods Resources";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/ExpoApplication_privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/EXConstants.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/ExpoConstants_privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/ExpoDevice_privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/ExpoFileSystem_privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/ExpoSystemUI_privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/RCT-Folly_privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/RNCAsyncStorage_resources.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/RNSVGFilters.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/React-Core_privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/React-cxxreact_privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/boost_privacy.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/glog_privacy.bundle",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-IsotopeAI/Pods-IsotopeAI-resources.sh\"\n";
			showEnvVarsInLog = 0;
			outputFileListPaths = (
			);
			inputFileListPaths = (
			);
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		13B07F871A680F5B00A75B9A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				F11748422D0307B40044C1D9 /* AppDelegate.swift in Sources */,
				7D97DE5C402C956999B7CDC8 /* ExpoModulesProvider.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		XXDC105B0E28F884B8E02DXX /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			runOnlyForDeploymentPostprocessing = 0;
			files = (
			);
		};
		XXE45443DC38DCFE1DB622XX /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			runOnlyForDeploymentPostprocessing = 0;
			files = (
			);
		};
		XXE5EC32CD2D09C6C886C7XX /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			runOnlyForDeploymentPostprocessing = 0;
			files = (
			);
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		XX4B53910FF94CC8DD0B2AXX /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = XX3F0FEC3A3780043C5071XX /* ShieldConfiguration */;
			targetProxy = XX7D740BCEA9150F638357XX /* PBXContainerItemProxy */;
		};
		XXF510CD31F2FCFD77BC98XX /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = XX3D32D7C943D37D77BB1BXX /* ShieldAction */;
			targetProxy = XXDDE06CF18B5502AA5505XX /* PBXContainerItemProxy */;
		};
		XXFE818A28A5955E5D90A3XX /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = XXA10EF079A7E8D1784A82XX /* ActivityMonitorExtension */;
			targetProxy = XX9C2CABAF796184CCCE70XX /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		13B07F941A680F5B00A75B9A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 67034C1C2E8FBF75BC10D542 /* Pods-IsotopeAI.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = IsotopeAI/IsotopeAI.entitlements;
				CURRENT_PROJECT_VERSION = 1;
				ENABLE_BITCODE = NO;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"FB_SONARKIT_ENABLED=1",
				);
				INFOPLIST_FILE = IsotopeAI/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				OTHER_SWIFT_FLAGS = "$(inherited) -D EXPO_CONFIGURATION_DEBUG";
				PRODUCT_BUNDLE_IDENTIFIER = com.isotopeai.app;
				PRODUCT_NAME = IsotopeAI;
				SWIFT_OBJC_BRIDGING_HEADER = "IsotopeAI/IsotopeAI-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				REACT_NATIVE_DEVICE_ACTIVITY_APP_GROUP = group.com.isotopeai.app.deviceactivity;
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
			};
			name = Debug;
		};
		13B07F951A680F5B00A75B9A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 0D786A50EF7ABD459845F80B /* Pods-IsotopeAI.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = IsotopeAI/IsotopeAI.entitlements;
				CURRENT_PROJECT_VERSION = 1;
				INFOPLIST_FILE = IsotopeAI/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				OTHER_SWIFT_FLAGS = "$(inherited) -D EXPO_CONFIGURATION_RELEASE";
				PRODUCT_BUNDLE_IDENTIFIER = com.isotopeai.app;
				PRODUCT_NAME = IsotopeAI;
				SWIFT_OBJC_BRIDGING_HEADER = "IsotopeAI/IsotopeAI-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				REACT_NATIVE_DEVICE_ACTIVITY_APP_GROUP = group.com.isotopeai.app.deviceactivity;
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
			};
			name = Release;
		};
		83CBBA201A601CBA00E9B192 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = "$(SDKROOT)/usr/lib/swift\"$(inherited)\"";
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_LDFLAGS = (
					"$(inherited)",
					" ",
				);
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) DEBUG";
				USE_HERMES = true;
				REACT_NATIVE_DEVICE_ACTIVITY_APP_GROUP = group.com.isotopeai.app.deviceactivity;
			};
			name = Debug;
		};
		83CBBA211A601CBA00E9B192 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = "$(SDKROOT)/usr/lib/swift\"$(inherited)\"";
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_LDFLAGS = (
					"$(inherited)",
					" ",
				);
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				USE_HERMES = true;
				VALIDATE_PRODUCT = YES;
				REACT_NATIVE_DEVICE_ACTIVITY_APP_GROUP = group.com.isotopeai.app.deviceactivity;
			};
			name = Release;
		};
		XX3A445D07268CE2943AF5XX /* Debug */ = {
			isa = XCBuildConfiguration;
			name = Debug;
			buildSettings = {
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GENERATE_INFOPLIST_FILE = YES;
				CURRENT_PROJECT_VERSION = 1;
				INFOPLIST_FILE = ../targets/ActivityMonitorExtension/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = ActivityMonitorExtension;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.0.0;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.isotopeai.app.ActivityMonitorExtension;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				CODE_SIGN_ENTITLEMENTS = ../targets/ActivityMonitorExtension/generated.entitlements;
			};
		};
		XX936B01988156938913DBXX /* Debug */ = {
			isa = XCBuildConfiguration;
			name = Debug;
			buildSettings = {
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GENERATE_INFOPLIST_FILE = YES;
				CURRENT_PROJECT_VERSION = 1;
				INFOPLIST_FILE = ../targets/ShieldConfiguration/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = ShieldConfiguration;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.0.0;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.isotopeai.app.ShieldConfiguration;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				CODE_SIGN_ENTITLEMENTS = ../targets/ShieldConfiguration/generated.entitlements;
			};
		};
		XXAFAAE2BEAA1AA40DEE67XX /* Release */ = {
			isa = XCBuildConfiguration;
			name = Release;
			buildSettings = {
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GENERATE_INFOPLIST_FILE = YES;
				CURRENT_PROJECT_VERSION = 1;
				INFOPLIST_FILE = ../targets/ActivityMonitorExtension/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = ActivityMonitorExtension;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.0.0;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.isotopeai.app.ActivityMonitorExtension;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				CODE_SIGN_ENTITLEMENTS = ../targets/ActivityMonitorExtension/generated.entitlements;
			};
		};
		XXB2F5E7310F8D5039BD9EXX /* Release */ = {
			isa = XCBuildConfiguration;
			name = Release;
			buildSettings = {
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GENERATE_INFOPLIST_FILE = YES;
				CURRENT_PROJECT_VERSION = 1;
				INFOPLIST_FILE = ../targets/ShieldConfiguration/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = ShieldConfiguration;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.0.0;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.isotopeai.app.ShieldConfiguration;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				CODE_SIGN_ENTITLEMENTS = ../targets/ShieldConfiguration/generated.entitlements;
			};
		};
		XXB32C06AA2CAB4F88B9B6XX /* Debug */ = {
			isa = XCBuildConfiguration;
			name = Debug;
			buildSettings = {
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GENERATE_INFOPLIST_FILE = YES;
				CURRENT_PROJECT_VERSION = 1;
				INFOPLIST_FILE = ../targets/ShieldAction/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = ShieldAction;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.0.0;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.isotopeai.app.ShieldAction;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				CODE_SIGN_ENTITLEMENTS = ../targets/ShieldAction/generated.entitlements;
			};
		};
		XXB3C46FC071C9E057DB23XX /* Release */ = {
			isa = XCBuildConfiguration;
			name = Release;
			buildSettings = {
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GENERATE_INFOPLIST_FILE = YES;
				CURRENT_PROJECT_VERSION = 1;
				INFOPLIST_FILE = ../targets/ShieldAction/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = ShieldAction;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.0.0;
				MTL_FAST_MATH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.isotopeai.app.ShieldAction;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				CODE_SIGN_ENTITLEMENTS = ../targets/ShieldAction/generated.entitlements;
			};
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "IsotopeAI" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				13B07F941A680F5B00A75B9A /* Debug */,
				13B07F951A680F5B00A75B9A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "IsotopeAI" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				83CBBA201A601CBA00E9B192 /* Debug */,
				83CBBA211A601CBA00E9B192 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		XX3733CB6C1678C0F8E742XX /* Build configuration list for PBXNativeTarget "ActivityMonitorExtension" */ = {
			isa = XCConfigurationList;
			defaultConfigurationIsVisible = 0;
			buildConfigurations = (
				XX3A445D07268CE2943AF5XX /* Debug */,
				XXAFAAE2BEAA1AA40DEE67XX /* Release */,
			);
			defaultConfigurationName = Release;
		};
		XXA7EB4A2DFBC8FC5DB310XX /* Build configuration list for PBXNativeTarget "ShieldAction" */ = {
			isa = XCConfigurationList;
			defaultConfigurationIsVisible = 0;
			buildConfigurations = (
				XXB32C06AA2CAB4F88B9B6XX /* Debug */,
				XXB3C46FC071C9E057DB23XX /* Release */,
			);
			defaultConfigurationName = Release;
		};
		XXBB75734F95ED04335AF2XX /* Build configuration list for PBXNativeTarget "ShieldConfiguration" */ = {
			isa = XCConfigurationList;
			defaultConfigurationIsVisible = 0;
			buildConfigurations = (
				XX936B01988156938913DBXX /* Debug */,
				XXB2F5E7310F8D5039BD9EXX /* Release */,
			);
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 83CBB9F71A601CBA00E9B192 /* Project object */;
}
